<template>
  <div class="recipe-detail">
    <!-- 头部区域 -->
    <div class="recipe-header" v-if="recipe">
      <div class="container">
        <div class="header-utility">
          <el-button link type="primary" @click="$router.push('/recipes')">
            <el-icon><ArrowLeft /></el-icon>
            返回菜谱
          </el-button>
        </div>

        <div class="header-content">
          <!-- 左侧图片 -->
          <div class="recipe-image-section">
            <div class="recipe-image">
              <img :src="recipe.image || '/placeholder-dish.jpg'" :alt="recipe.name" />
            </div>
          </div>

          <!-- 右侧信息 -->
          <div class="recipe-info-section">
            <div class="recipe-badge" v-if="recipe.author?.role === 'INHERITOR'">
              <span>传承人作品</span>
            </div>

            <h1 class="recipe-name">{{ recipe.name }}</h1>
            <h2 class="recipe-dialect">{{ recipe.dialectName }}</h2>

            <div class="recipe-region">
              <el-icon><Location /></el-icon>
              <span>{{ recipe.region }}</span>
            </div>

            <!-- 音频播放 -->
            <div class="audio-section" v-if="recipe.dialectAudio">
              <div class="audio-label">
                <el-icon><Microphone /></el-icon>
                方言发音
              </div>
              <audio controls class="audio-player">
                <source :src="recipe.dialectAudio" type="audio/mpeg">
                您的浏览器不支持音频播放
              </audio>
            </div>

            <!-- 关键信息（Chip） -->
            <div class="meta-chips">
              <span class="chip" v-if="recipe.difficulty">
                <el-icon><Star /></el-icon>
                {{ getDifficultyText(recipe.difficulty) }}
              </span>
              <span class="chip" v-if="recipe.cookTime">
                <el-icon><Clock /></el-icon>
                {{ recipe.cookTime }}分钟
              </span>
              <span class="chip" v-if="recipe.servings">
                <el-icon><User /></el-icon>
                {{ recipe.servings }}人份
              </span>
              <span class="chip">
                <el-icon><View /></el-icon>
                {{ recipe.viewCount }}次浏览
              </span>
            </div>

            <!-- 作者和操作 -->
            <div class="recipe-footer">
              <div class="author-info">
                <span class="author-label">作者：</span>
                <span
                  class="author-name clickable"
                  @click="goToInheritorProfile(recipe.author?.id)"
                >
                  {{ recipe.author?.username }}
                </span>
                <span class="author-role" v-if="recipe.author?.role === 'INHERITOR'">传承人</span>
              </div>
              <div class="recipe-actions">
                <FavoriteButton
                  :recipe-id="recipe.id"
                  @favorite-changed="handleFavoriteChanged"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->

    <div class="main-content" v-if="recipe" v-loading="loading">
      <div class="container">
        <div class="content-grid">

          <!-- 左侧内容 -->
          <div class="content-left">
            <!-- 食材卡片（已移动到右侧） -->

            <!-- 制作步骤卡片 -->
            <div class="content-card steps-card">
              <h3 class="card-title">
                <el-icon><Document /></el-icon>
                制作步骤
              </h3>
              <div class="steps-list">
                <div
                  v-for="(step, index) in parsedSteps"
                  :key="index"
                  class="step-item"
                >
                  <div class="step-number">{{ index + 1 }}</div>
                  <div class="step-content">{{ step.description }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧内容 -->
          <div class="content-right">
            <!-- 菜谱介绍 -->
            <div class="content-card description-card" v-if="cleanedRecipe?.description">
              <h3 class="card-title">
                <el-icon><InfoFilled /></el-icon>
                菜谱介绍
              </h3>
              <div class="description-content">
                <p>{{ cleanedRecipe.description }}</p>
              </div>
            </div>

            <!-- 所需食材 -->
            <div class="content-card ingredients-card" v-if="parsedIngredients.length">
              <h3 class="card-title">
                <el-icon><ShoppingCart /></el-icon>
                所需食材
              </h3>
              <div class="ingredients-grid">
                <div
                  v-for="(ingredient, index) in parsedIngredients"
                  :key="index"
                  class="ingredient-item"
                >
                  <div class="ingredient-name">{{ ingredient.name }}</div>
                  <div class="ingredient-amount">{{ ingredient.amount }}</div>
                </div>
              </div>
            </div>

            <!-- 文化传承信息 -->
            <div class="content-card cultural-card" v-if="hasCulturalContent">
              <h3 class="card-title">
                <el-icon><Document /></el-icon>
                文化传承
              </h3>
              <div class="cultural-content">
                <el-tabs type="border-card" class="cultural-tabs">
                  <el-tab-pane
                    v-if="cleanedRecipe?.culturalHistory"
                    label="🏛️ 历史渊源"
                  >
                    <div class="cultural-text">
                      <p>{{ cleanedRecipe.culturalHistory }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="cleanedRecipe?.regionalFeature"
                    label="🗺️ 地域特色"
                  >
                    <div class="cultural-text">
                      <p>{{ cleanedRecipe.regionalFeature }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="cleanedRecipe?.culturalMeaning"
                    label="🎭 文化寓意"
                  >
                    <div class="cultural-text">
                      <p>{{ cleanedRecipe.culturalMeaning }}</p>
                    </div>
                  </el-tab-pane>

                  <el-tab-pane
                    v-if="cleanedRecipe?.inheritanceStory"
                    label="👴 传承故事"
                  >
                    <div class="cultural-text">
                      <p>{{ cleanedRecipe.inheritanceStory }}</p>
                    </div>
                  </el-tab-pane>
                </el-tabs>
              </div>
            </div>
          </div>
        </div>

        <!-- 评论区 -->
        <div class="comments-section">
          <CommentSection
            :recipe-id="recipe.id"
            @comment-added="handleCommentAdded"
            @comment-updated="handleCommentUpdated"
            @comment-deleted="handleCommentDeleted"
          />
        </div>
      </div>
    </div>

    <div v-else-if="!loading" class="not-found">
      <div class="container">
        <el-result
          icon="warning"
          title="菜谱不存在"
          sub-title="您访问的菜谱可能已被删除或不存在"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/recipes')">
              返回菜谱列表
            </el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getRecipeById } from '../api/recipes.js'
import { ElMessage } from 'element-plus'
import {
  Location,
  Star,
  Clock,
  User,
  View,
  ShoppingCart,
  Document,
  InfoFilled,
  Microphone,
  ArrowLeft
} from '@element-plus/icons-vue'
import FavoriteButton from '../components/FavoriteButton.vue'
import CommentSection from '../components/CommentSection.vue'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const recipe = ref(null)

// 清理和修复JSON字符串的函数
const cleanJsonString = (str) => {
  if (!str || typeof str !== 'string') return str

  // 移除HTML注释
  let cleaned = str.replace(/<!--[\s\S]*?-->/g, '')

  // 修复常见的JSON格式问题
  cleaned = cleaned
    .replace(/,\s*}/g, '}')  // 移除对象末尾多余的逗号
    .replace(/,\s*]/g, ']')  // 移除数组末尾多余的逗号
    .trim()

  // 修复未正确转义的引号和格式错误
  // 特别处理steps字段中可能出现的格式问题
  if (cleaned.includes('","}]')) {
    // 修复类似 '","}]' 的错误格式
    cleaned = cleaned.replace(/","\s*}]/g, '"}]')
  }

  // 修复未闭合的引号
  if (cleaned.endsWith('",')) {
    cleaned = cleaned.slice(0, -2) + '"'
  }

  // 如果字符串以单引号开头和结尾，移除它们
  if (cleaned.startsWith("'") && cleaned.endsWith("'")) {
    cleaned = cleaned.slice(1, -1)
  }

  return cleaned
}

// 解析JSON格式的食材和步骤
const parsedIngredients = computed(() => {
  if (!recipe.value?.ingredients) return []
  try {
    if (typeof recipe.value.ingredients === 'string') {
      const cleanedStr = cleanJsonString(recipe.value.ingredients)
      return JSON.parse(cleanedStr)
    }
    return recipe.value.ingredients
  } catch (error) {
    console.error('解析食材数据失败:', error, recipe.value.ingredients)
    return []
  }
})

const parsedSteps = computed(() => {
  if (!recipe.value?.steps) return []
  try {
    if (typeof recipe.value.steps === 'string') {
      const cleanedStr = cleanJsonString(recipe.value.steps)
      return JSON.parse(cleanedStr)
    }
    return recipe.value.steps
  } catch (error) {
    console.error('解析步骤数据失败:', error, recipe.value.steps)
    return []
  }
})

const getDifficultyText = (difficulty) => {
  const texts = ['', '简单', '容易', '中等', '困难', '专家']
  return texts[difficulty] || '未知'
}



// 清理文本内容的函数
const cleanTextContent = (text) => {
  if (!text || typeof text !== 'string') return text

  // 移除HTML注释、多余的引号和格式错误
  return text
    .replace(/<!--[\s\S]*?-->/g, '')  // 移除HTML注释
    .replace(/^['"]|['"]$/g, '')      // 移除开头和结尾的引号
    .replace(/\\n/g, '\n')            // 处理转义的换行符
    .replace(/'\s*,\s*'/g, '')        // 移除类似 ', ' 的格式错误
    .replace(/^,\s*/, '')             // 移除开头的逗号
    .replace(/\s*,$/, '')             // 移除结尾的逗号
    .replace(/"\s*$/, '')             // 移除结尾未闭合的引号
    .trim()
}

// 清理后的文本字段
const cleanedRecipe = computed(() => {
  if (!recipe.value) return null

  return {
    ...recipe.value,
    description: cleanTextContent(recipe.value.description),
    culturalHistory: cleanTextContent(recipe.value.culturalHistory),
    regionalFeature: cleanTextContent(recipe.value.regionalFeature),
    culturalMeaning: cleanTextContent(recipe.value.culturalMeaning),
    inheritanceStory: cleanTextContent(recipe.value.inheritanceStory)
  }
})

// 判断是否有文化传承内容
const hasCulturalContent = computed(() => {
  if (!cleanedRecipe.value) return false
  return !!(
    cleanedRecipe.value.culturalHistory ||
    cleanedRecipe.value.regionalFeature ||
    cleanedRecipe.value.culturalMeaning ||
    cleanedRecipe.value.inheritanceStory
  )
})

const fetchRecipe = async () => {
  try {
    loading.value = true
    const response = await getRecipeById(route.params.id)
    recipe.value = response.recipe
  } catch (error) {
    console.error('获取菜谱详情失败:', error)
    ElMessage.error('获取菜谱详情失败')
  } finally {
    loading.value = false
  }
}

const handleFavoriteChanged = (data) => {
  // 可以在这里更新本地的收藏数量等
  console.log('收藏状态改变:', data)
}

const handleCommentAdded = () => {
  // 评论添加后可以刷新菜谱信息以更新评论数量
  fetchRecipe()
}

const handleCommentUpdated = () => {
  // 评论更新后的处理
  console.log('评论已更新')
}

const handleCommentDeleted = () => {
  // 评论删除后可以刷新菜谱信息以更新评论数量
  fetchRecipe()
}

// 跳转到传承人个人主页
const goToInheritorProfile = (authorId) => {
  if (authorId) {
    router.push(`/inheritor/${authorId}`)
  }
}

onMounted(() => {
  fetchRecipe()
})
</script>

<style scoped>
.recipe-detail {
  min-height: 100vh;
  background: #f8fafc;
}



/* 头部区域样式 */
.header-utility{display:flex;justify-content:flex-start;margin-bottom:12px}
.recipe-header {
  background: #fff;
  padding: 28px 0 24px;
  border-bottom: 1px solid #e2e8f0;
}

.header-content {
  display: grid;
  grid-template-columns: minmax(320px, 480px) 1fr;
  gap: 40px;
  align-items: stretch;
}

.recipe-image-section {
  display: flex;
  justify-content: center;
  height: 100%;
}

.recipe-image {
  border-radius: var(--el-border-radius-base);
  overflow: hidden;
  box-shadow: var(--el-box-shadow);
  width: 100%;
  height: 100%;
  min-height: 360px;
  background: #f8fafc; /* 添加背景色，避免加载时的空白 */
  position: relative;
}

.recipe-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
  display: block;
  background: #f8fafc; /* 图片加载前的背景 */
}

.recipe-image:hover img {
  transform: scale(1.02); /* 减小缩放效果，避免过于突兀 */
}

.recipe-info-section {
  padding: 20px 0;
}

.recipe-badge {
  display: inline-block;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  color: white;
  border-radius: 50px;
  padding: 8px 16px;
  margin-bottom: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: var(--el-box-shadow);
}

.recipe-name {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 12px;
  color: #1e293b;
  line-height: 1.2;
}

.recipe-dialect {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--el-color-primary);
  font-style: italic;
}

.recipe-region {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  margin-bottom: 24px;
  color: #64748b;
}

.audio-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--el-border-radius-base);
  padding: 20px;
  margin-bottom: 24px;
}

.audio-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 12px;
  font-size: 1rem;
  color: #334155;
}

.audio-player {
  width: 100%;
  height: 40px;
  border-radius: 8px;
}

.recipe-meta-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--el-border-radius-base);
  transition: all 0.3s ease;
}

.meta-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.meta-icon {
  font-size: 1.2rem;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.meta-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.meta-label {
  font-size: 0.85rem;
  color: #64748b;
}

.meta-value {
  font-size: 1rem;
  font-weight: 600;
  color: #334155;
}

.recipe-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: var(--el-border-radius-base);
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-label {
  font-size: 0.9rem;
  color: #64748b;
}

.author-name {
  font-weight: 600;
  font-size: 1rem;
  color: #334155;
}

.author-name.clickable {
  color: var(--el-color-primary);
  cursor: pointer;
  transition: color 0.3s ease;
}

.author-name.clickable:hover {
  color: var(--el-color-primary-dark-2);
  text-decoration: underline;
}

.author-role {
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* 主要内容区域 */
.main-content {
  background: #f8fafc;
  padding: 60px 0;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}

.content-left,
.content-right {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-card {
  background: white;
  border-radius: var(--el-border-radius-base);
  padding: 30px;
  box-shadow: var(--el-box-shadow);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: var(--el-box-shadow-dark);
  transform: translateY(-2px);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.4rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.card-title .el-icon {
  font-size: 1.5rem;
  color: var(--el-color-primary);
}

/* 食材卡片 */
.ingredients-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.ingredient-item {
  background: #f8fafc;
  border-radius: var(--el-border-radius-base);
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.ingredient-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.ingredient-name {
  display: block;
  font-weight: 600;
  color: #334155;
  margin-bottom: 4px;
  font-size: 1rem;
}

.ingredient-amount {
  display: block;
  color: #64748b;
  font-size: 0.9rem;
}

/* 制作步骤 */
.steps-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  padding: 20px;
  background: #f8fafc;
  border-radius: var(--el-border-radius-base);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.step-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.step-number {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-1));
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.1rem;
}

.step-content {
  flex: 1;
  line-height: 1.6;
  color: #334155;
  font-size: 1rem;
}

/* 描述内容 */
.description-content {
  line-height: 1.8;
  color: #475569;
  font-size: 1rem;
}

.description-content p {
  margin: 0;
}

/* 文化传承 */
.cultural-tabs{border:none}
.cultural-tabs :deep(.el-tabs__header){margin-bottom:12px}
.cultural-tabs :deep(.el-tabs__nav-wrap){background:#f8fafc;border-radius:var(--el-border-radius-base);padding:4px}
.cultural-tabs :deep(.el-tabs__item){border:none;border-radius:8px;margin:0 2px;transition:all .25s ease}
.cultural-tabs :deep(.el-tabs__item.is-active){background:#fff;color:var(--el-color-primary);box-shadow:0 2px 8px rgba(0,0,0,.08)}

/* 元信息 Chips */
.meta-chips{display:flex;flex-wrap:wrap;gap:8px 10px;margin:12px 0 10px}
.chip{display:inline-flex;align-items:center;gap:6px;background:#f8fafc;border:1px solid #e2e8f0;padding:8px 12px;border-radius:999px;color:#334155;font-size:.9rem}
.chip .el-icon{color:var(--el-color-primary)}

.cultural-text {
  line-height: 1.8;
  color: #475569;
  font-size: 1rem;
  padding: 20px;
  background: #f8fafc;
  border-radius: var(--el-border-radius-base);
  border: 1px solid #e2e8f0;
}

.cultural-text p {
  margin: 0;
}

/* 评论区 */
.comments-section {
  background: white;
  padding: 30px 20px;
  margin-top: 30px;
  border-top: 1px solid #e2e8f0;
}

/* 错误页面 */
.not-found {
  min-height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .header-content {
    grid-template-columns: minmax(320px, 420px) 1fr;
    gap: 28px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .recipe-meta-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .recipe-header {
    padding: 20px 0;
  }

  .header-content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .recipe-image-section {
    justify-content: center;
  }

  .recipe-image {
    max-width: 300px;
    height: 225px; /* 保持比例 */
    min-height: 225px;
    margin: 0 auto;
  }

  .recipe-name {
    font-size: 2rem;
  }

  .recipe-dialect {
    font-size: 1.2rem;
  }

  .content-card {
    padding: 20px;
  }

  .ingredients-grid {
    grid-template-columns: 1fr;
  }

  .recipe-meta-grid {
    grid-template-columns: 1fr;
  }

  .meta-item {
    padding: 12px;
  }

  .recipe-footer {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .step-item {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .step-number {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .recipe-name {
    font-size: 1.8rem;
  }

  .recipe-meta-cards {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 40px 0;
  }

  .content-left,
  .content-right {
    gap: 20px;
  }
}
</style>
